import styled from "styled-components/native";
import { Pressable } from "react-native";

export const StyledBottomSheetView = styled.View`
  flex: 1;
  padding: 20px;
`;

export const Title = styled.Text`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 20px;
  text-align: center;
`;

export const SearchContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 20px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const SearchInput = styled.TextInput`
  flex: 1;
  margin-left: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const ItemsCountText = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 16px;
`;

export const ListContainer = styled.FlatList`
  flex: 1;
`;

export const EntityItem = styled(Pressable)<{ isSelected?: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: 12px;
  border-width: 2px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.border};
  margin-bottom: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const EntityInfo = styled.View`
  flex: 1;
`;

export const EntityName = styled.Text`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const EntityId = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const EmptyContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
`;

export const EmptyText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  margin-bottom: 20px;
`;

export const AddManualButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 12px 20px;
  border-radius: 8px;
`;

export const AddManualText = styled.Text`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.white};
  margin-left: 8px;
`;
